import { describe, it, expect, vi, beforeEach } from 'vitest'
import { ref } from 'vue'
import { useQualificationRejectReason } from '../useQualificationRejectReason'

// Mock Vuex store
const mockStore = {
  getters: {
    'rejectReason/rejectDetailList': []
  }
}

vi.mock('vuex', () => ({
  useStore: () => mockStore
}))

describe('useQualificationRejectReason', () => {
  beforeEach(() => {
    // 重置 mock store
    mockStore.getters['rejectReason/rejectDetailList'] = []
  })

  describe('parseQualificationFieldCode', () => {
    it('应该正确解析图片错误的fieldCode', () => {
      const { parseQualificationFieldCode } = useQualificationRejectReason()
      
      const fieldCode = 'merchantCategoryDraft.categoryQualificationMap*64240f7b7d13e90001fa7d01.qualificationList*1097420507324764160.fileAttachmentList'
      const result = parseQualificationFieldCode(fieldCode)
      
      expect(result).toEqual({
        categoryId: '64240f7b7d13e90001fa7d01',
        qualificationId: '1097420507324764160',
        fieldType: 'image',
        qualificationName: undefined,
        rawFieldCode: fieldCode,
        debugInfo: expect.objectContaining({
          originalFieldCode: fieldCode
        })
      })
    })

    it('应该正确解析资质类型错误的fieldCode', () => {
      const { parseQualificationFieldCode } = useQualificationRejectReason()
      
      const fieldCode = 'merchantCategoryDraft.categoryQualificationMap*64240f7b7d13e90001fa7d01.qualificationList*补充材料（非必填）'
      const result = parseQualificationFieldCode(fieldCode)
      
      expect(result).toEqual({
        categoryId: '64240f7b7d13e90001fa7d01',
        qualificationName: '补充材料（非必填）',
        fieldType: 'qualification',
        qualificationId: undefined,
        rawFieldCode: fieldCode,
        debugInfo: expect.objectContaining({
          originalFieldCode: fieldCode
        })
      })
    })

    it('应该处理可能遗漏fileAttachmentList后缀的纯数字ID', () => {
      const { parseQualificationFieldCode } = useQualificationRejectReason()
      
      const fieldCode = 'merchantCategoryDraft.categoryQualificationMap*64240f7b7d13e90001fa7d01.qualificationList*1097420507324764160'
      const result = parseQualificationFieldCode(fieldCode)
      
      expect(result.fieldType).toBe('image')
      expect(result.qualificationId).toBe('1097420507324764160')
    })

    it('应该处理复杂的多层级ID格式', () => {
      const { parseQualificationFieldCode } = useQualificationRejectReason()
      
      const fieldCode = 'merchantCategoryDraft.categoryQualificationMap*64240f7b7d13e90001fa7d01.qualificationList*ID1.ID2.fileAttachmentList'
      const result = parseQualificationFieldCode(fieldCode)
      
      expect(result.fieldType).toBe('image')
      expect(result.qualificationId).toBe('ID1.ID2')
    })

    it('应该处理无效的fieldCode', () => {
      const { parseQualificationFieldCode } = useQualificationRejectReason()
      
      const fieldCode = 'invalid.fieldCode'
      const result = parseQualificationFieldCode(fieldCode)
      
      expect(result.fieldType).toBe('unknown')
    })
  })

  describe('getQualificationRejectReason', () => {
    it('应该匹配图片错误的驳回原因', () => {
      // 设置 mock 数据
      mockStore.getters['rejectReason/rejectDetailList'] = [
        {
          moduleCode: 'QUALIFICATION',
          fieldCode: 'merchantCategoryDraft.categoryQualificationMap*64240f7b7d13e90001fa7d01.qualificationList*1097420507324764160.fileAttachmentList',
          rejectContent: '图片不符合要求',
          fieldName: '类目资质图片'
        }
      ]

      const { getQualificationRejectReason } = useQualificationRejectReason('64240f7b7d13e90001fa7d01')
      
      const result = getQualificationRejectReason('1097420507324764160', 'image')
      
      expect(result).toBe('图片不符合要求')
    })

    it('应该匹配资质类型错误的驳回原因', () => {
      // 设置 mock 数据
      mockStore.getters['rejectReason/rejectDetailList'] = [
        {
          moduleCode: 'QUALIFICATION',
          fieldCode: 'merchantCategoryDraft.categoryQualificationMap*64240f7b7d13e90001fa7d01.qualificationList*补充材料（非必填）',
          rejectContent: '资质类型选择错误',
          fieldName: '类目资质'
        }
      ]

      const { getQualificationRejectReason } = useQualificationRejectReason('64240f7b7d13e90001fa7d01')
      
      const result = getQualificationRejectReason('', 'qualification', '补充材料（非必填）')
      
      expect(result).toBe('资质类型选择错误')
    })

    it('应该支持模糊匹配', () => {
      // 设置 mock 数据
      mockStore.getters['rejectReason/rejectDetailList'] = [
        {
          moduleCode: 'QUALIFICATION',
          fieldCode: 'merchantCategoryDraft.categoryQualificationMap*64240f7b7d13e90001fa7d01.qualificationList*1097420507324764160.fileAttachmentList',
          rejectContent: '图片不符合要求',
          fieldName: '类目资质图片'
        }
      ]

      const { getQualificationRejectReason } = useQualificationRejectReason('64240f7b7d13e90001fa7d01')
      
      // 使用包含关系进行模糊匹配
      const result = getQualificationRejectReason('109742050732476', 'image')
      
      expect(result).toBe('图片不符合要求')
    })

    it('应该返回空字符串当没有匹配的驳回原因时', () => {
      const { getQualificationRejectReason } = useQualificationRejectReason('64240f7b7d13e90001fa7d01')
      
      const result = getQualificationRejectReason('nonexistent', 'image')
      
      expect(result).toBe('')
    })
  })

  describe('generateFieldCodeForDisplay', () => {
    it('应该生成正确的图片字段fieldCode', () => {
      const { generateFieldCodeForDisplay } = useQualificationRejectReason('64240f7b7d13e90001fa7d01')
      
      const result = generateFieldCodeForDisplay('1097420507324764160', 'image')
      
      expect(result).toBe('merchantCategoryDraft.categoryQualificationMap*64240f7b7d13e90001fa7d01.qualificationList*1097420507324764160.fileAttachmentList')
    })

    it('应该生成正确的资质类型字段fieldCode', () => {
      const { generateFieldCodeForDisplay } = useQualificationRejectReason('64240f7b7d13e90001fa7d01')
      
      const result = generateFieldCodeForDisplay('', 'qualification', '补充材料（非必填）')
      
      expect(result).toBe('merchantCategoryDraft.categoryQualificationMap*64240f7b7d13e90001fa7d01.qualificationList*补充材料（非必填）')
    })

    it('应该处理没有categoryId的情况', () => {
      const { generateFieldCodeForDisplay } = useQualificationRejectReason()
      
      const result = generateFieldCodeForDisplay('1097420507324764160', 'image')
      
      expect(result).toBe('merchantCategoryDraft.categoryQualificationMap*.qualificationList*1097420507324764160.fileAttachmentList')
    })
  })
})
