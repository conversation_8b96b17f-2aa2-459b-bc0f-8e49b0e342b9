# QualificationCard 驳回原因匹配逻辑修复

## 🎯 修复目标

修复QualificationCard组件中的校验错误显示逻辑，确保能正确处理复杂的驳回数据格式，特别是包含多个ID、特殊字符等复杂情况。

## 📋 驳回数据格式分析

### 原始驳回数据示例
```json
[
  {
    "rejectContent": "您好，您填写的店铺名称不符合平台命名规范...",
    "fieldName": "类目资质图片",
    "moduleCode": "QUALIFICATION",
    "fieldCode": "merchantCategoryDraft.categoryQualificationMap*64240f7b7d13e90001fa7d01.qualificationList*1097420507324764160.fileAttachmentList"
  },
  {
    "fieldCode": "merchantCategoryDraft.categoryQualificationMap*64240f7b7d13e90001fa7d01.qualificationList*补充材料（非必填）",
    "rejectContent": "您好，您填写的店铺名称不符合平台命名规范...",
    "fieldName": "类目资质",
    "moduleCode": "QUALIFICATION"
  }
]
```

### fieldCode字段解析规则
1. `merchantCategoryDraft.categoryQualificationMap` - 匹配资质卡片的基础路径
2. `*64240f7b7d13e90001fa7d01.qualificationList` - 匹配当前资质卡片的categoryId
3. `*1097420507324764160.fileAttachmentList` - 匹配资质卡片中的图片附件（数字ID）
4. `*补充材料（非必填）` - 匹配资质名称（中文名称）

## 🔧 修复内容

### 1. 增强 parseQualificationFieldCode 函数

**修复前的问题：**
- 正则表达式无法处理复杂的ID格式
- 缺乏对边界情况的处理
- 没有调试信息

**修复后的改进：**
```typescript
// 增强的正则表达式，支持多层级ID
const qualificationListMatch = fieldCode.match(/qualificationList\*([^.]+(?:\.[^.]+)*?)(?:\.(fileAttachmentList))?$/)

// 智能判断纯数字ID（可能遗漏fileAttachmentList后缀）
if (/^\d+$/.test(qualificationIdOrName)) {
  qualificationId = qualificationIdOrName
  fieldType = 'image'
  console.warn(`检测到可能遗漏fileAttachmentList后缀的图片ID: ${qualificationIdOrName}`)
}

// 添加调试信息
debugInfo: {
  categoryMatch: categoryMatch || undefined,
  qualificationListMatch: qualificationListMatch || undefined,
  originalFieldCode: fieldCode
}
```

### 2. 改进 getQualificationRejectReason 匹配逻辑

**修复前的问题：**
- 只支持精确匹配，无法处理ID不一致的情况
- 缺乏模糊匹配策略

**修复后的改进：**
```typescript
if (fieldType === 'image') {
  // 策略1：精确匹配qualificationId
  if (parsed.qualificationId === qualificationCode) {
    return true
  }
  
  // 策略2：模糊匹配 - 检查包含关系
  if (parsed.qualificationId && qualificationCode && 
      (parsed.qualificationId.includes(qualificationCode) || 
       qualificationCode.includes(parsed.qualificationId))) {
    return true
  }
  
  // 策略3：categoryId匹配的通用错误
  if (!qualificationCode && parsed.categoryId && categoryId && parsed.categoryId === categoryId) {
    return true
  }
}
```

### 3. 新增专用的资质字段匹配策略

**新增功能：**
- 在 `useRejectReason.ts` 中添加 `getRejectContentByQualificationFieldCode` 方法
- 专门处理资质字段的复杂匹配逻辑
- 支持categoryId匹配和资质部分的模糊匹配

```typescript
getRejectContentByQualificationFieldCode: (fieldCode: string, joinSeparator: string = '; ') => {
  // 提取关键部分进行匹配
  const extractQualificationInfo = (code: string) => {
    const categoryMatch = code.match(/categoryQualificationMap\*([^.]+)/)
    const qualificationMatch = code.match(/qualificationList\*([^.]+)(?:\.(.+))?$/)
    return {
      categoryId: categoryMatch?.[1],
      qualificationPart: qualificationMatch?.[1],
      suffix: qualificationMatch?.[2]
    }
  }
  
  // 智能匹配逻辑
  // ...
}
```

### 4. 更新 RejectReasonDisplay 组件

**新增匹配策略：**
- 添加 `'qualification'` 匹配策略
- 对资质相关字段自动使用专用匹配逻辑

```typescript
case 'qualification':
  // 专门用于资质字段的智能匹配
  return rejectReasonQuery.getRejectContentByQualificationFieldCode(props.fieldCode)

case 'fuzzy':
default:
  // 对于资质相关字段，优先使用资质专用匹配策略
  if (props.fieldCode.includes('merchantCategoryDraft.categoryQualificationMap')) {
    const qualificationResult = rejectReasonQuery.getRejectContentByQualificationFieldCode(props.fieldCode)
    if (qualificationResult) {
      return qualificationResult
    }
  }
  return rejectReasonQuery.getRejectContentByFuzzyFieldCode(props.fieldCode)
```

### 5. 组件集成更新

**QualificationCard组件：**
- 添加 `categoryId` 到 `componentProps` 类型定义
- 将 `categoryId` 传递给子组件

**QualificationAndItem/QualificationOrItem组件：**
- 添加 `categoryId` 到 props
- 更新 RejectReasonDisplay 使用 `match-strategy="qualification"`

## 🧪 测试验证

创建了完整的单元测试文件 `useQualificationRejectReason.test.ts`，覆盖：
- fieldCode解析的各种情况
- 匹配逻辑的精确和模糊匹配
- 边界情况处理
- fieldCode生成逻辑

## 🎉 修复效果

### 修复前的问题：
1. ❌ 无法匹配复杂的fieldCode格式
2. ❌ 图片错误的ID匹配失败
3. ❌ 缺乏模糊匹配能力
4. ❌ 没有调试信息

### 修复后的改进：
1. ✅ 支持复杂的多层级ID格式
2. ✅ 智能的图片ID匹配（精确+模糊+通用）
3. ✅ 专用的资质字段匹配策略
4. ✅ 完整的调试信息和错误处理
5. ✅ 向后兼容，不影响现有功能

## 🔍 调试支持

在开发环境下，修复后的代码会输出详细的调试信息：
```javascript
console.log(`[QualificationRejectReason] 解析fieldCode:`, {
  input: fieldCode,
  output: result
})

console.log(`[QualificationRejectReason] 匹配结果:`, {
  qualificationCode,
  fieldType,
  qualificationName,
  categoryId,
  totalReasons: qualificationRejectReasons.value.length,
  matchedCount: matchingReasons.length,
  result: result || '无匹配结果'
})
```

## 📝 使用说明

修复后的组件使用方式保持不变，但现在能够正确处理复杂的驳回数据格式：

```vue
<RejectReasonDisplay
  :field-code="generateFieldCodeForDisplay(qualificationCode, 'image')"
  :current-value="currentImages"
  :original-value="originalImages"
  match-strategy="qualification"
/>
```

新的 `match-strategy="qualification"` 会自动使用增强的匹配逻辑，确保能够正确匹配用户提供的驳回数据格式。
